trigger:
  branches:
    include:
    - dev


pool:
  name: 'devops-agent'

variables:
  group: yUsage-QA
  dockerHubRepoBackend: 'yoizensa/yusage-backend-service'
  dockerHubRepoFrontend: 'yoizensa/yusage-frontend-service'
  containerRegistry: 'docker-hub'

stages:
- stage: BuildPush
  displayName: 'Build and Push Docker Images'
  jobs:
  - job: BuildPushAPI
    displayName: 'Build and Push API Docker Image'
    pool:
      name: 'devops-agent'
    steps:
    - script: |
        docker system prune -af
      displayName: 'Clean Docker Cache'

    - task: Docker@2
      displayName: 'Build and Push API Docker Image'
      inputs:
        command: buildAndPush
        repository: $(dockerHubRepoBackend)
        Dockerfile: api/Dockerfile.prod
        tags: |
          latest

        containerRegistry: $(containerRegistry)

  - job: BuildPushClient
    displayName: 'Build and Push Client Docker Image'
    pool:
      name: 'devops-agent'
    steps:
    - script: |
        docker system prune -af
      displayName: 'Clean Docker Cache'

    - task: Docker@2
      displayName: 'Build and Push Client Docker Image'
      inputs:
        command: buildAndPush
        repository: $(dockerHubRepoFrontend)
        Dockerfile: client/Dockerfile.prod
        tags: |
          latest
        containerRegistry: $(containerRegistry)
        arguments: --build-arg API_URL=$(API_URL) --build-arg CLIENT_PORT=$(CLIENT_PORT) --build-arg GOOGLE_CLIENT_ID=$(GOOGLE_CLIENT_ID)

  - job: Restart_k8s
    displayName: 'Restart QA'
    dependsOn:
    - BuildPushAPI
    - BuildPushClient
    steps:
    - task: KubectlInstaller@0
      displayName: 'Install Kubectl'

    - task: Kubernetes@1
      displayName: 'Restart API Deployment'
      inputs:
        connectionType: 'Azure Resource Manager'
        azureSubscriptionEndpoint: 'aks-yoizen'
        azureResourceGroup: 'k8s-rg'
        kubernetesCluster: 'aks-yoizen'
        namespace: 'yusage-qa'
        command: rollout
        arguments: 'restart deployment yusage-api'

    - task: Kubernetes@1
      displayName: 'Restart Frontend Deployment'
      inputs:
        connectionType: 'Azure Resource Manager'
        azureSubscriptionEndpoint: 'aks-yoizen'
        azureResourceGroup: 'k8s-rg'
        kubernetesCluster: 'aks-yoizen'
        namespace: 'yusage-qa'
        command: rollout
        arguments: 'restart deployment yusage-fe'
