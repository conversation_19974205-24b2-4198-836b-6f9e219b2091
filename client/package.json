{"name": "yoizen-console-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@cyriacbr/react-split-text": "^1.0.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^8.3.1", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.17.9", "@tanstack/react-table": "^8.11.3", "axios": "^1.6.5", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^3.0.6", "dotenv": "^16.4.5", "framer-motion": "^10.17.4", "i18next": "^23.7.16", "lucide-react": "^0.303.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-flags-select": "^2.2.3", "react-hook-form": "^7.49.2", "react-i18next": "^14.0.0", "react-router-dom": "^6.21.1", "recharts": "^2.10.3", "shadcn-ui": "^0.6.0", "sonner": "^1.3.1", "switch": "^0.0.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}