import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useForm, Controller } from 'react-hook-form'
import * as z from 'zod'
import {
  Form,
  FormControl,
  FormItem,
  FormLabel,
  FormDescription,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useTranslation } from 'react-i18next'
import { updateCounter } from '@/api/counters'
import { Button } from '@/components/ui/button'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Select,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from '@/components/ui/select'
import {
  Card,
  CardHeader,
  CardDescription,
  CardContent,
} from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Us, Br } from 'react-flags-select'
import { Switch } from '@/components/ui/switch'
import { useEffect, useContext } from 'react'
import { AuthContext } from '@/context/authContext'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import {
  useCounter,
  useCounterGroups,
  useCounterTypes,
  useCounterUnits,
} from '@/hooks/useCounters'
import { Label } from '@/components/ui/label'
import { Translation } from '@/models/translation'
import { Counter } from '@/models/counter'

const counterFormSchema = z.object({
  name: z.string().min(3).max(100),
  description: z.string().min(5).max(160),
  groupId: z.string().min(1),
  typeId: z.string().min(1),
  unitId: z.string().min(1).optional(),
  active: z.boolean(),
  name_en: z.string().optional(),
  description_en: z.string().optional(),
  name_pt: z.string().optional(),
  description_pt: z.string().optional(),
  translations: z
    .array(
      z.object({
        name: z.string().min(3).max(100),
        description: z.string().min(5).max(160),
        language: z.string().min(2).max(2),
      })
    )
    .optional(),
})

type CounterFormValues = z.infer<typeof counterFormSchema>

export function CounterForm() {
  const { id } = useParams()

  const navigate = useNavigate()

  const { data } = useCounter(id)

  const findTranslationField = (
    language: string,
    field: string
  ): string | undefined => {
    return data?.translations?.find(
      (translation: Translation) =>
        translation.language.code === language &&
        translation.field === field
    )?.translation
  }

  const getEmptyCounterForm = (data?: Counter) => ({
    name: data?.name || '',
    description: data?.description || '',
    groupId: data?.groupId?.toString() || '',
    typeId: data?.typeId?.toString() || '',
    unitId: data?.unitId ? data.unitId.toString() : undefined,
    active: !!data?.active,
    name_en: findTranslationField('en', 'name') || '',
    description_en: findTranslationField('en', 'description') || '',
    name_pt: findTranslationField('pt', 'name') || '',
    description_pt: findTranslationField('pt', 'description') || '',
  })

  const form = useForm<CounterFormValues>({
    resolver: zodResolver(counterFormSchema),
    defaultValues: {
      ...getEmptyCounterForm(data),
    },
  })

  useEffect(() => {
    form.reset({ ...getEmptyCounterForm(data) })
  }, [data])

  const { t } = useTranslation()
  const { getUserInfo } = useContext(AuthContext)
  const { role } = getUserInfo()
  const isAdmin = role === 'admin'

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = form

  function onSubmit(formData: CounterFormValues) {
    const translations = [
      {
        value: formData.name_en,
        field: 'name',
        language: 'en',
      },
      {
        value: formData.description_en,
        field: 'description',
        language: 'en',
      },
      {
        value: formData.name_pt,
        field: 'name',
        language: 'pt',
      },
      {
        value: formData.description_pt,
        field: 'description',
        language: 'pt',
      },
    ]

    mutate({ ...formData, id: id, translations })
  }

  const queryClient = useQueryClient()
  const { mutate } = useMutation({
    mutationFn: updateCounter,
    onSuccess: (data) => {
      if (data) {
        toast.success(t('messageCounterSave'))

        queryClient.invalidateQueries({
          queryKey: [`counterId-${id}`],
        })

        queryClient.invalidateQueries({ queryKey: ['counters'] })
        queryClient.invalidateQueries({ queryKey: ['counterGroups'] })

        navigate('/counters')
      }
    },
  })

  const { data: counterUnits } = useCounterUnits()
  const { data: counterTypes } = useCounterTypes()
  const { data: counterGroups } = useCounterGroups()

  const onCancel = () => {
    navigate('/counters')
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          <div className="flex flex-col space-y-2">
            <label htmlFor="name">{t('name')}</label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  disabled={!isAdmin}
                  id="name"
                  placeholder={t('namePlaceholder')}
                  {...field}
                />
              )}
            />
            {errors.name && (
              <p className="text-red-500">{errors.name.message}</p>
            )}
          </div>
          <div className="flex flex-col space-y-2">
            <label htmlFor="description">{t('description')}</label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Input
                  disabled={!isAdmin}
                  id="description"
                  placeholder={t('descriptionPlaceHolder')}
                  {...field}
                />
              )}
            />
            {errors.description && (
              <p className="text-red-500">
                {errors.description.message}
              </p>
            )}
          </div>

          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <label htmlFor="name" className="pb-2">
              {t('group')}
            </label>

            <Controller
              name="groupId"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  disabled={!isAdmin}
                  onValueChange={(value) => field.onChange(value)}
                  value={field.value.toString()}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectorCounter')} />
                  </SelectTrigger>
                  <SelectContent>
                    {counterGroups?.map((counter) => (
                      <SelectItem
                        key={counter.id}
                        value={counter.id.toString()}
                      >
                        <span className="font-medium">
                          {counter.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.groupId && (
              <p className="text-red-500">{errors.groupId.message}</p>
            )}
          </div>

          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <label htmlFor="name" className="pb-2">
              {t('counterType')}
            </label>
            <Controller
              name="typeId"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  disabled={!isAdmin}
                  onValueChange={(value) => field.onChange(value)}
                  value={field.value.toString()}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t('selectorCounterType')}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {counterTypes?.map((counterType) => (
                      <SelectItem
                        key={counterType.id}
                        value={counterType.id.toString()}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <span className="font-medium">
                          {counterType.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.typeId && (
              <p className="text-red-500">{errors.typeId.message}</p>
            )}
          </div>

          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <label htmlFor="name" className="pb-2">
              {t('unit')}
            </label>
            <Controller
              name="unitId"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  disabled={!isAdmin}
                  onValueChange={(value) => field.onChange(value)}
                  value={field?.value?.toString()}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectorUnit')} />
                  </SelectTrigger>
                  <SelectContent>
                    {counterUnits?.map((counterUnit) => (
                      <SelectItem
                        key={counterUnit.id}
                        value={counterUnit.id.toString()}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <span className="font-medium">
                          {counterUnit.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.unitId && (
              <p className="text-red-500">{errors.unitId.message}</p>
            )}
          </div>

          <Controller
            control={control}
            name="active"
            render={({ field: { value, onChange } }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('activeCounterSwitcher')}
                  </FormLabel>
                  <FormDescription>
                    {t('descriptionSwitcher')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    disabled={!isAdmin}
                    name="active"
                    onCheckedChange={onChange}
                    checked={value}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <Card>
            <CardHeader>
              <Label>{t('translation')}</Label>
              <CardDescription>{t('addTranslation')}</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <Us className="mr-2 inline-block aling-middle" />
              <div className="grid gap-2">
                <Label htmlFor="name">{t('name')}</Label>
                <Controller
                  control={control}
                  name="name_en"
                  render={({ field }) => (
                    <Input
                      id="name_en"
                      disabled={!isAdmin}
                      placeholder={t('namePlaceholder')}
                      {...field}
                    />
                  )}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="number">{t('description')}</Label>
                <Controller
                  control={control}
                  name="description_en"
                  render={({ field }) => (
                    <Input
                      id="description_en"
                      disabled={!isAdmin}
                      placeholder={t('descriptionPlaceHolder')}
                      {...field}
                    />
                  )}
                />
              </div>
            </CardContent>
            <Separator> </Separator>
            <br></br>

            <CardContent className="grid gap-6">
              <Br className="mr-2 inline-block aling-middle" />
              <div className="grid gap-2">
                <Label htmlFor="name">{t('name')}</Label>
                <Controller
                  control={control}
                  name="name_pt"
                  render={({ field }) => (
                    <Input
                      id="name_pt"
                      disabled={!isAdmin}
                      placeholder={t('namePlaceholder')}
                      {...field}
                    />
                  )}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="number">{t('description')}</Label>
                <Controller
                  control={control}
                  name="description_pt"
                  render={({ field }) => (
                    <Input
                      id="description_pt"
                      disabled={!isAdmin}
                      placeholder={t('descriptionPlaceHolder')}
                      {...field}
                    />
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-5">
            <Button variant="secondary" onClick={() => onCancel()}>
              {t('cancel')}
            </Button>
            {isAdmin && <Button>{t('save')}</Button>}
          </div>
        </form>
      </Form>
    </>
  )
}
