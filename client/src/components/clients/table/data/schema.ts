import { z } from 'zod'

export const clientSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullable().optional(),
  active: z.boolean(),
  isTestClient: z.boolean(),
  modules: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
    })
  ),
  counters: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
    })
  ),
})

export type Client = z.infer<typeof clientSchema>
