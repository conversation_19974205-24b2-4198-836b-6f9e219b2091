import * as React from 'react';
import { CheckIcon, PlusCircledIcon } from '@radix-ui/react-icons';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useTranslation } from 'react-i18next';

interface Entity {
	id: number;
	name: string;
}

interface EntitySelectionFilterProps {
	entities: Entity[];
	selectedEntitiesIds: number[];
	onSelectionChange: (selectedEntitiesIds: number[]) => void;
	title?: string;
	handleMetrics: () => void;
}

export function EntitySelectionFilter({
	entities,
	selectedEntitiesIds,
	onSelectionChange,
	title = 'Select',
	handleMetrics,
}: EntitySelectionFilterProps) {
	const [selectedIds, setSelectedIds] = React.useState(new Set(selectedEntitiesIds));
	const [inputString, setInputString] = React.useState('');
	const { t } = useTranslation();

	React.useEffect(() => {
		setSelectedIds(new Set(selectedEntitiesIds));
	}, [selectedEntitiesIds]);

	const toggleEntitySelection = (entityId: number) => {
		setSelectedIds((prevSelectedIds) => {
			const newSelectedIds = new Set(prevSelectedIds);
			if (newSelectedIds.has(entityId)) {
				newSelectedIds.delete(entityId);
			} else {
				newSelectedIds.add(entityId);
			}
			// Call onSelectionChange here to prevent the loop
			onSelectionChange(Array.from(newSelectedIds));
			return newSelectedIds;
		});
	};

	return (
		<Popover
			onOpenChange={(isOpen) => {
				if (!isOpen) {
					handleMetrics();
				}
			}}
		>
			<PopoverTrigger asChild>
				<Button variant="outline" size="sm" className="h-10 border-dashed p-4 justify-start md:justify-center">
					<PlusCircledIcon className="mr-2 h-4 w-4" />
					{title}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[200px] p-0" align="start">
				<Command>
					<CommandInput placeholder={title} value={inputString} onValueChange={setInputString} />
					<CommandList>
						<CommandEmpty>{t('noResultsFound')}</CommandEmpty>
						<CommandGroup>
							<div className="w-full flex justify-center items-center py-1">
								<CommandItem
									onSelect={() => onSelectionChange([])}
									disabled={selectedIds.size === 0}
									className={`w-1/2 flex-col text-center border border-secondary transition-all duration-200 ${
										selectedIds.size === 0
											? 'pointer-events-none cursor-not-allowed'
											: 'cursor-pointer'
									}`}
								>
									{t('clear')}
								</CommandItem>
							</div>
							{entities.map((entity) => {
								const isSelected = selectedIds.has(entity.id);
								return (
									<CommandItem
										key={entity.id}
										onSelect={() => toggleEntitySelection(entity.id)}
										className="transition-all duration-200"
									>
										<div
											className={`mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary ${
												isSelected
													? 'bg-primary text-primary-foreground'
													: 'opacity-50 [&_svg]:invisible'
											}`}
										>
											<CheckIcon className="h-4 w-4" />
										</div>
										<span>{entity.name}</span>
									</CommandItem>
								);
							})}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
