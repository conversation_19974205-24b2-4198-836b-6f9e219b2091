import {
  Card,
  CardDescription,
  CardTitle,
  Card<PERSON>eader,
  CardContent,
} from '@/components/ui/card'
import { FormControl, FormLabel } from '@/components/ui/form'
import { Checkbox } from '@/components/ui/checkbox'
import { Controller, Control } from 'react-hook-form'

export function CheckboxCard({
  control,
  items,
  name,
  title,
  description,
  defaultValue,
  disabled,
}: {
  control: Control<any>
  items: { id: string; label: string }[]
  name: string
  title: string
  description: string
  defaultValue: number[]
  disabled?: boolean
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-6">
        {items.map((item) => (
          <div
            key={item.id}
            className="flex flex-row items-start space-x-3 space-y-0"
          >
            <FormControl>
              <Controller
                control={control}
                name={name}
                render={({ field: { onChange, value } }) => {
                  return (
                    <Checkbox
                      disabled={disabled}
                      checked={
                        defaultValue.includes(item.id) ||
                        value.includes(item.id)
                      }
                      onCheckedChange={(checked) => {
                        if (checked) {
                          onChange([...value, item.id])
                        } else {
                          onChange(value.filter((v) => v !== item.id))
                        }
                      }}
                    />
                  )
                }}
              />
            </FormControl>
            <FormLabel className="font-normal">
              {item.label}
            </FormLabel>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
