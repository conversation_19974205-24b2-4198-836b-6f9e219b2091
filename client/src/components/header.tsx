import { UserNav } from '@/components/user-nav'
import { MainNav } from '@/components/main-nav'
import { AuthContext } from '@/context/authContext'
import { useContext, useEffect, useState } from 'react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import yoizenLogo from '/yoizenlogo.png'

const Header = () => {
  const { isLoggedIn, getUserInfo } = useContext(AuthContext)
  const { role } = getUserInfo()
  const isClient = role === 'client'
  const [version, setVersion] = useState('')

  useEffect(() => {
    import('../../package.json').then((pkg) => {
      setVersion(pkg.version)
    })
  }, [])

  return (
    <>
      <div className="border-b">
        <div className="flex h-16 items-center px-8">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <img src={yoizenLogo} alt="Yoizen Logo" className="h-8" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs text-muted-foreground">v{version}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {isLoggedIn && (
            <>
              {!isClient && <MainNav className="mx-8 md:mx-12" />}
              <div className="ml-auto flex items-center space-x-4">
                <UserNav />
              </div>
            </>
          )}
        </div>
      </div>
    </>
  )
}

export default Header
