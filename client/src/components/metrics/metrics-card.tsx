import React from 'react'
import {
  MetricsCard as Card, // Importar la nueva clase MetricsCard
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

interface Props {
  value: string | undefined | number
  title: string | undefined
  type: string
  icon: React.ReactNode
  isActive?: boolean
  reference?: string | number
}

const MetricsCard = ({
  title,
  value,
  type,
  icon,
  reference,
  isActive = true,
}: Props) => {
  const inactiveClass = !isActive ? 'opacity-80 grayscale' : ''

  const arr = value?.toString().split(' ')
  const valueNumber = arr ? parseFloat(arr[0]) : 0
  const unit = arr ? arr[1] : ''

  return (
    <Card className={`${inactiveClass} transition duration-300`}>
      <CardHeader className="flex flex-col gap-2 md:gap-0 md:flex-row md:items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="flex-shrink-0">{icon}</div>
      </CardHeader>
      <CardContent>
      <div
          className={`text-2xl font-bold ${inactiveClass}`} 
          style={{ color: 'var(--metrics-card-foreground)' }} 
        >
          {valueNumber} {reference ? `/ ${reference}` : ''} {unit}
        </div>
        <p
          className={`text-xs text-muted-foreground italic ${inactiveClass}`}
        >
          {type}
        </p>
      </CardContent>
    </Card>
  )
}

export default MetricsCard
