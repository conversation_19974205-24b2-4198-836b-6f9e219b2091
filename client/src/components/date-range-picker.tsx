import { useContext } from 'react';
import { CalendarIcon } from '@radix-ui/react-icons';
import { Locale, format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { es, enUS, ptBR } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { AppContext } from '@/context/appContext';

export function CalendarDateRangePicker({
	className,
	selectedDateRange,
	onDateChange,
	handleMetrics,
}: React.HTMLAttributes<HTMLDivElement> & {
	selectedDateRange: DateRange | undefined;
	onDateChange: (newDateRange: DateRange | undefined) => void;
	handleMetrics: () => void;
}) {
	const { language } = useContext(AppContext);
	const { t } = useTranslation();
	let localeConfig = enUS;
	if (language === 'es') {
		localeConfig = es;
	} else if (language === 'pt') {
		localeConfig = ptBR;
	}

	const formatDatePascalCase = (date: Date, localeConfig: Locale) => {
		// Format the date as usual
		let formattedDate = format(date, 'LLL dd, y', {
			locale: localeConfig,
		});

		// Capitalize the first letter of the month abbreviation
		formattedDate = formattedDate.replace(/^[a-z]/, (match) => match.toUpperCase());

		return formattedDate;
	};

	return (
		<div className={cn('grid gap-2', className)}>
			<Popover
				onOpenChange={(isOpen) => {
					if (!isOpen) {
						handleMetrics();
					}
				}}
			>
				<PopoverTrigger asChild>
					<Button
						id="date"
						variant={'outline'}
						className={cn(
							'w-full md:w-[260px] justify-start text-left font-normal',
							!selectedDateRange && 'text-muted-foreground'
						)}
					>
						<CalendarIcon className="mr-2 h-4 w-4" />
						{selectedDateRange?.from ? (
							selectedDateRange.to ? (
								<>
									{formatDatePascalCase(selectedDateRange.from, localeConfig)} -{' '}
									{formatDatePascalCase(selectedDateRange.to, localeConfig)}
								</>
							) : (
								formatDatePascalCase(selectedDateRange.from, localeConfig)
							)
						) : (
							<span>{t('pickDate')}</span>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-auto p-0" align="end">
					<Calendar
						locale={localeConfig}
						initialFocus
						mode="range"
						defaultMonth={selectedDateRange?.from}
						selected={selectedDateRange}
						onSelect={onDateChange}
						numberOfMonths={2}
					/>
				</PopoverContent>
			</Popover>
		</div>
	);
}
