import {
  Dialog,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useTranslation } from 'react-i18next'
import { AppContext } from '@/context/appContext'
import { useContext, useEffect, useState } from 'react'
import { toast } from 'sonner'
import { Us, Br, Ar } from 'react-flags-select'

interface Props {
  isOpened: boolean
  onOpenChange: (isOpened: boolean) => void
}

export function SettingsDialog({ isOpened, onOpenChange }: Props) {
  const { t } = useTranslation()

  const { language, changeLanguage } = useContext(AppContext)

  useEffect(() => {
    setSelectedLanguage(language)
  }, [language])

  const [selectedLanguage, setSelectedLanguage] = useState('')

  const onClickContinue = () => {
    changeLanguage(selectedLanguage)
    onOpenChange(false)
    localStorage.setItem('language', selectedLanguage)
  }

  const onClickCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={isOpened} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('languageConfiguration')}</DialogTitle>
        </DialogHeader>
        <div>
          <div className="space-y-4 py-2 pb-4">
            <div className="space-y-2">
              <Label htmlFor="plan">{t('selectorLanguage')}</Label>
              <Select
                value={selectedLanguage}
                onValueChange={setSelectedLanguage}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('selectorLanguage')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    value="es"
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Ar className="mr-2 inline-block aling-middle" />
                    <span className="font-medium">
                      {t('spanish')}{' '}
                    </span>
                  </SelectItem>
                  <SelectItem value="en">
                    <Us className="mr-2 inline-block aling-middle" />
                    <span className="font-medium">
                      {t('english')}{' '}
                    </span>
                  </SelectItem>
                  <SelectItem value="pt">
                    <Br className="mr-2 inline-block aling-middle" />
                    <span className="font-medium">
                      {t('portuguese')}{' '}
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="secondary" onClick={onClickCancel}>
            {t('cancel')}
          </Button>
          <Button
            value="submit"
            onClick={() => {
              onClickContinue()
              toast.success(t('toast'))
            }}
          >
            {t('continue')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
