import React, { useState, useEffect } from 'react';

interface ToastProps {
  message: string;
}

const Toast: React.FC<ToastProps> = ({ message }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 3000); // Ocultar después de 3 segundos

    return () => clearTimeout(timer);
  }, []);

  return isVisible ? (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded shadow">
      {message}
    </div>
  ) : null;
};

export default Toast;