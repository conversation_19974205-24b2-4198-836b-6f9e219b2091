/* eslint-disable no-prototype-builtins */
/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useContext, useCallback } from 'react';
import { startOfMonth, format, parseISO, subMonths, subDays } from 'date-fns';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DateRange } from 'react-day-picker';
import { CalendarDateRangePicker } from '@/components/date-range-picker';
import { useTranslation } from 'react-i18next';
import { Separator } from '@/components/ui/separator';
import MetricsCard from '@/components/metrics/metrics-card';
import { EntitySelectionFilter } from '@/components/ui/entity-selection-filter';
import { useClients } from '@/hooks/useClients';
import { clientSchema } from '@/components/clients/table/data/schema';
import { z } from 'zod';
import { useCounterGroups } from '@/hooks/useCounters';
import { useMetricsReport } from '@/hooks/useMetrics';
import { Badge } from '@/components/ui/badge';
import { FileDown } from 'lucide-react';
import { AuthContext } from '@/context/authContext';
import { Client } from '@/components/clients/table/data/schema';
import Counter from '@/types/counter';
import CounterGroup from '@/types/counterGroup';

import { fetchMetrics } from '@/api/metrics';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { getCounterWithUnits } from '@/lib/utils';
import { TranslatableEntity, getTranslation } from '@/lib/utils';
import { AppContext } from '@/context/appContext';
import Loader from '@/components/ui/loader';

const Dashboard = () => {
    const { t } = useTranslation();
    const { getUserInfo } = useContext(AuthContext);
    const { language, changeMetricsSearch, clientIds: ctxClientIds, dateRange: ctxDateRange } = useContext(AppContext);
    const { client: loggedClientName, role } = getUserInfo();
    const isClient = role === 'client';
    const { data: clientsData, isLoading: isLoadingClients } = useClients(true, ['clients-no-test']);
    const { data: counterGroups, isLoading: isLoadingGroups } = useCounterGroups();

    // Parsear los datos de clientes
    const clients = clientsData && z.array(clientSchema).parse(clientsData);

    const today = new Date();
    const initialStartDate = subMonths(startOfMonth(today), 1);
    const initialEndDate = subDays(startOfMonth(today), 1);
    const [isLoading, setIsLoading] = useState(false);
    const [clientIds, setClientIds] = useState<number[]>(ctxClientIds || []);
    const [results, setResults] = useState<any | undefined>(undefined);

    const [title, setTitle] = useState<string>('Yoizen');
    const [subtitle, setSubtitle] = useState<string>(t('monthly_consumption_metrics'));
    const [initialLoad, setInitialLoad] = useState(true);
    const [shouldFetchReport, setShouldFetchReport] = useState(false);
    const [dateRange, setDateRange] = useState<DateRange | undefined>({
        from: ctxDateRange?.from || initialStartDate,
        to: ctxDateRange?.to || initialEndDate,
    });

    useEffect(() => {
        if (role !== 'client') {
            // Para admin/viewer, usar los clientIds del contexto
            setClientIds(ctxClientIds || []);
        } else {
            // Para usuarios client, usar solo su clientId específico
            const { clientId } = getUserInfo();
            if (clientId) {
                const clientIdNumber = parseInt(clientId, 10);
                if (!isNaN(clientIdNumber)) {
                    setClientIds([clientIdNumber]);
                }
            }
        }
    }, [role, ctxClientIds]); // Removido getUserInfo para evitar re-renders innecesarios

    const getMetrics = useCallback(async () => {
        try {
            setIsLoading(true);
            const data = await fetchMetrics({
                clientIds,
                dateFrom: format(dateRange?.from as Date, 'yyyy-MM-dd'),
                dateTo: format(dateRange?.to as Date, 'yyyy-MM-dd'),
            });
            setResults(data);
            // Solo actualizar el contexto global para usuarios admin/viewer
            // Los usuarios client no deben actualizar el contexto global para evitar bucles
            if (role !== 'client') {
                changeMetricsSearch(clientIds, dateRange as DateRange, data);
            }
        } catch (error) {
            console.error('Error fetching metrics', error);
        } finally {
            setInitialLoad(false);
            setIsLoading(false);
        }
    }, [clientIds, dateRange, changeMetricsSearch, role]);

    // Efecto separado para llamar getMetrics cuando clientIds o dateRange cambien
    useEffect(() => {
        if (clientIds.length > 0 && dateRange?.from && dateRange?.to) {
            getMetrics();
        }
    }, [getMetrics]); // Ahora que getMetrics incluye role en sus dependencias, es seguro incluirlo

    useMetricsReport({
        clientIds,
        dateRange: {
            from: dateRange?.from,
            to: dateRange?.to,
        },
        shouldFetchReport,
    });

    useEffect(() => {
        if (shouldFetchReport) {
            setShouldFetchReport(false);
        }
    }, [shouldFetchReport]);

    useEffect(() => {
        if (clients && role !== 'client') {
            if (clientIds.length > 1) {
                setTitle(`${clientIds.length} ${t('selected')}`);
                const clientsList = clients
                    .filter((client) => clientIds.some((clientId) => clientId === client.id))
                    .map((client) => client.name);
                setSubtitle(clientsList.join(', '));
            } else if (clientIds.length === 1) {
                const clientFound = clients.find((client) => client.id === clientIds[0]);
                if (clientFound) {
                    setTitle(clientFound.name);
                    setSubtitle(clientFound.description ?? t('no_subtitle'));
                }
            } else {
                setTitle('Yoizen');
                setSubtitle(t('monthly_consumption_metrics'));
            }
        } else {
            setTitle(loggedClientName ?? 'Yoizen');
        }
    }, [clientIds, clients]);

    const handleSelectionChange = (newSelectedIds: number[]) => {
        setClientIds(newSelectedIds);
    };

    const handleDateChange = (newDateRange: DateRange | undefined) => {
        setDateRange(newDateRange);
    };



    const onClickSearch = () => {
        getMetrics();
    };

    const onClickDownload = () => {
        setShouldFetchReport(true);
    };

    const getMetricContent = (counter: Counter) => {
        if (results) {
            const metric = results[counter.code];

            if (!metric) return {};

            let value = 0;
            if (metric.totalSum && metric.totalSum !== 0) {
                value = metric.totalSum;
            }
            else if (metric.maxSum && metric.maxSum !== 0) {
                value = metric.maxSum;
            }
            // Si ambos son 0 o undefined, usar el que corresponda al tipo
            else {
                if (metric.counter_type === "count") {
                    value = metric.totalSum ?? 0;
                } else {
                    value = metric.maxSum ?? 0;
                }
            }

            const description = metric.maxDay
                ? `${t("happenedOn")} ${format(parseISO(metric.maxDay), "dd/MM/yyyy")}`
                : "";

            return {
                value,
                description,
                reference: metric.reference,
            };
        }
        return {};
    };

    const getCounterGroups = () => {
        const groups =
            counterGroups?.filter((cg: CounterGroup) => {
                if (cg.counters.length > 0) {
                    return cg.counters.some((counter) => results?.hasOwnProperty(counter.code));
                }
                return false;
            }) || [];

        return groups;
    };

    const isCounterActiveForClient = (counter: Counter) => {
        // --> If more than one client is selected, we show all of them as active
        if (clientIds.length > 1 || clientIds.length === 0) return true;

        const foundClient = clients?.find((cl) => cl.id === clientIds[0]);
        return !!foundClient && !!foundClient.counters.find((ct) => ct.id === counter.id);
    };

    const getCounterGroupDescription = (counterGroup: CounterGroup) => {
        const counters = counterGroup.counters;

        if (!results) return '';

        const hasStatistics = counters.some((counter: Counter) => {
            const metric = results[counter.code];
            return metric?.maxSum || metric?.maxDay || metric?.totalSum;
        });

        if (!hasStatistics || Object.keys(results).length === 0) {
            return `${t('messageAgents')}`;
        }

        return counterGroup.description;
    };

    const getCounterFieldTranslated = (counter: Counter, field: string): string | undefined => {
        const counterTranslation = getTranslation(
            language,
            'counter',
            {
                id: counter?.id,
                translations: counter?.translations,
            } as TranslatableEntity,
            field
        );

        return counterTranslation?.translation ?? counter?.name;
    };

    if (isLoadingClients || isLoadingGroups) {
        return <Loader />;
    }

    return (
        <div className="flex flex-col">
            <div className="flex-1 space-y-8 md:p-8 md:pt-6">
                <div className="flex-row items-center justify-between space-y-2 md:flex">
                    <div className="mb-4 md:mb-0">
                        <h2 className="text-5xl font-bold tracking-tight mb-2">{title}</h2>
                        <p className="text-sm text-muted-foreground">{subtitle}</p>
                    </div>
                    <div className="flex flex-col gap-4 md:flex-row md:gap-2 md:flex md:items-center md:space-x-2">
                        {clients && !loggedClientName && (
                            <EntitySelectionFilter
                                entities={clients}
                                selectedEntitiesIds={clientIds}
                                onSelectionChange={handleSelectionChange}
                                title={t('clientsButton')}
                                handleMetrics={getMetrics}
                            />
                        )}

                        <CalendarDateRangePicker
                            selectedDateRange={dateRange}
                            onDateChange={handleDateChange}
                            handleMetrics={getMetrics}
                        />

                        <Button
                            onClick={onClickSearch}
                            disabled={clientIds.length === 0 || !dateRange?.from || !dateRange?.to}
                        >
                            {t('refresh')}
                        </Button>
                        {results && (
                            <Button
                                variant="outline"
                                className="bg-transparent border border-white text-white flex items-center"
                                onClick={onClickDownload}
                            >
                                <FileDown className="mr-2 h-4 w-4" />
                                {t('download')}
                            </Button>
                        )}
                    </div>
                </div>

                {isLoading && <Loader />}

                {/* TIME ZONE WARNING */}
                {isClient && (
                    <Alert className="rounded flex items-center border border-white w-fit">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>{t('timeZoneDescription')}</AlertDescription>
                    </Alert>
                )}

                {/* NO RESULTS WARNING */}
                {(getCounterGroups().length === 0 || !results) && !initialLoad && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4 " />
                        <AlertTitle>{t('titleReminder')}</AlertTitle>
                        <AlertDescription>{t('statisticsReminder')}</AlertDescription>
                    </Alert>
                )}

                {results && !isLoading && (
                    <div className="grid gap-4">
                        {getCounterGroups().map((counterGroup: CounterGroup, idx: number) => (
                            <div key={counterGroup.id}>
                                <CardHeader className="pl-0">
                                    <CardTitle>{counterGroup.name}</CardTitle>
                                    <CardDescription>{getCounterGroupDescription(counterGroup)}</CardDescription>
                                </CardHeader>
                                <CardContent className="grid gap-8 grid-cols-2 lg:grid-cols-4 pl-0">
                                    {counterGroup.counters
                                        .filter((ct) => results?.hasOwnProperty(ct.code))
                                        .map((counter: Counter) => {
                                            const { value, description, reference } = getMetricContent(counter);
                                            if (value === undefined || value === null) return null;
                                            const unitValue = getCounterWithUnits(counter.unit.name, parseFloat(String(value)));

                                            return (
                                                <MetricsCard
                                                    key={`${counter.name}-${counterGroup.id}`}
                                                    value={unitValue}
                                                    reference={reference}
                                                    title={getCounterFieldTranslated(counter, 'name')}
                                                    type={description}
                                                    icon={<Badge variant="default">{counter.type.name}</Badge>}
                                                    isActive={isCounterActiveForClient(counter)}
                                                />
                                            );
                                        })}
                                </CardContent>
                                {idx !== getCounterGroups().length - 1 && <Separator className="mt-4" />}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default Dashboard;
