import { z } from 'zod'
import { columns } from '@/components/clients/table/columns'
import { DataTable } from '@/components/clients/table/table'
import { clientSchema } from '@/components/clients/table/data/schema'
import { useClients } from '@/hooks/useClients'
import { useTranslation } from 'react-i18next'
import Loader from '@/components/ui/loader'

export default function Clients() {
  const { t } = useTranslation()
  const { data, isLoading } = useClients()

  const clients = data && z.array(clientSchema).parse(data)

  if (isLoading) {
    return <Loader />
  }

  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight capitalize">
            {t('clients')}
          </h2>
          <p className="text-muted-foreground">{t('clients_list')}</p>
        </div>
      </div>
      <DataTable data={clients} columns={columns} />
    </div>
  )
}
