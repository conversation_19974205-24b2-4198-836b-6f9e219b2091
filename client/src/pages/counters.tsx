import { useCounterGroups, useCounters } from '@/hooks/useCounters'
import { AlertTriangle } from 'lucide-react'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'
import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'
import { MultipleContainers } from '@/components/counters/sortable/sortable-multiple-containers'
import { UniqueIdentifier } from '@dnd-kit/core'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import CounterGroup from '@/types/counterGroup'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import Loader from '@/components/ui/loader'
import { reorderCounters } from '@/api/counters'
import Counter from '@/types/counter'

type Items = Record<UniqueIdentifier, UniqueIdentifier[]>

export default function Counters() {
  const { t } = useTranslation()
  const [hasChangedOrder, setHasChangedOrder] = useState(false)
  const { data, isLoading } = useCounterGroups()
  const { data: counters } = useCounters()

  const [initialListItems, setInitialListItems] = useState<
    Items | undefined
  >(undefined)

  const [orderedGroups, setOrderedGroups] = useState<string[]>([])
  const [orderedCounters, setOrderedCounters] = useState<
    Record<string, Counter[]>
  >({})
  const queryClient = useQueryClient()

  useEffect(() => {
    if (data) {
      const obj = {} as Items
      data.forEach((group: CounterGroup) => {
        obj[group.name] = group.counters.map(
          (counter) => counter.name
        )
      })

      setInitialListItems(obj)
    }
  }, [data])

  const { mutate } = useMutation({
    mutationFn: reorderCounters,
    onSuccess: (data) => {
      if (data) {
        toast.success(t('countersReorderSuccess'))
        setHasChangedOrder(false)
        queryClient.invalidateQueries({ queryKey: ['counters'] })
        queryClient.invalidateQueries({ queryKey: ['counterGroups'] })
      }
    },
  })

  if (isLoading) {
    return <Loader />
  }

  const onUpdateOrderedGroups = (groups: string[]) => {
    setOrderedGroups(groups)
  }
  const onUpdateOrderedCounters = (
    countersList: Record<string, Counter[]>
  ) => {
    setOrderedCounters(countersList)
  }

  const onDragItem = () => {
    setHasChangedOrder(true)
  }

  const onSaveCountersOrder = () => {
    const groups = orderedGroups.map((group: string) => {
      return {
        name: group,
        counters: orderedCounters[group],
      }
    })

    mutate(groups)
  }

  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex md:w-3/4 mx-auto">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight capitalize">
            {t('counters')}
          </h2>
        </div>
      </div>
      {hasChangedOrder && (
        <Alert>
          <AlertTriangle color="white" className="h-4 w-4 " />
          <AlertTitle>{t('titleReminder')}</AlertTitle>
          <AlertDescription>{t('subtitleReminder')}</AlertDescription>
        </Alert>
      )}

      {initialListItems && (
        <MultipleContainers
          vertical
          items={initialListItems}
          onUpdateContainer={onUpdateOrderedGroups}
          onUpdateItem={onUpdateOrderedCounters}
          counters={counters}
          onDragItem={onDragItem}
        />
      )}

      {hasChangedOrder && (
        <div className="flex justify-end space-x-5">
          <Button onClick={onSaveCountersOrder}>{t('save')}</Button>
        </div>
      )}
    </div>
  )
}
