import { Counter } from '@/models/counter'
import apiInstance from './api'
import ApiResponse from '../types/apiResponse'
import CounterGroup from '@/types/counterGroup'
import { CounterType } from '@/types/counterType'
import { CounterUnit } from '@/types/counterUnit'

export const fetchCounters = async (): Promise<
  ApiResponse<Counter[]>
> => {
  try {
    const response = await apiInstance.get('/counters')
    return response.data
  } catch (error) {
    console.error('Error fetching counters', error)
    throw error
  }
}

export const fetchCounterGroups = async (): Promise<
  CounterGroup[]
> => {
  try {
    const response = await apiInstance.get('/counters/groups')
    return response.data
  } catch (error) {
    console.error('Error fetching counter groups', error)
    throw error
  }
}

export const fetchCounter = async (
  id: string | undefined
): Promise<ApiResponse<Counter>> => {
  try {
    const response = await apiInstance.get(`/counters/${id}`)
    return response.data
  } catch (error) {
    console.error('Error fetching counter', error)
    throw error
  }
}

export const fetchCounterTypes = async (): Promise<
  ApiResponse<CounterType[]>
> => {
  try {
    const response = await apiInstance.get('/counters/types')
    return response.data
  } catch (error) {
    console.error('Error fetching counter groups', error)
    throw error
  }
}

export const fetchCounterUnits = async (): Promise<
  ApiResponse<CounterUnit[]>
> => {
  try {
    const response = await apiInstance.get('/counters/units')
    return response.data
  } catch (error) {
    console.error('Error fetching counter units', error)
    throw error
  }
}

export const updateCounter = async (
  counter: Counter
): Promise<ApiResponse<Counter>> => {
  try {
    const response = await apiInstance.put(
      `/counters/${counter.id}`,
      counter
    )
    return response.data
  } catch (error) {
    console.error('Error updating counter', error)
    throw error
  }
}

export const reorderCounters = async (groups: {
  name: string
  counters: Counter[]
}): Promise<ApiResponse<CounterGroup[]>> => {
  try {
    const response = await apiInstance.put(
      '/counters/groups/reorder',
      { groups }
    )
    return response.data
  } catch (error) {
    console.error('Error reordering groups', error)
    throw error
  }
}
