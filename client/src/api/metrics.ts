import { DateRange } from 'react-day-picker'
import apiInstance from './api'
import Metric from '@/types/metric'
import ApiResponse from '@/types/apiResponse'

export interface FetchMetricsParams {
  clientIds: number[]
  dateFrom: string
  dateTo: string
}

export interface IExcelParams {
  clientIds: number[]
  dateRange: DateRange
}

export const fetchMetrics = async ({
  clientIds,
  dateFrom,
  dateTo,
}: FetchMetricsParams): Promise<Metric> => {
  const params = new URLSearchParams()
  
  if (clientIds && clientIds.length > 0) {
    params.append('clientIds', clientIds.join(','))
  }
  
  params.append('dateFrom', dateFrom)
  params.append('dateTo', dateTo)
  
  const response = await apiInstance.get(`/metrics?${params}`)

  return response.data
}

export const fetchMetricsReport = async ({
  clientIds,
  dateFrom,
  dateTo,
}: FetchMetricsParams): Promise<ApiResponse<any>> => {
  try {
    const params = new URLSearchParams({
      clientIds: clientIds.join(','),
      dateFrom,
      dateTo,
    })
    const response = await apiInstance.get(
      `/metrics/report?${params}`,
      {
        responseType: 'blob',
      }
    )

    downloadExcel(response.data)

    return response.data
  } catch (error) {
    console.error('Error fetching metrics report ', error)
    throw error
  }
}

const downloadExcel = async (buffer: any) => {
  const fileURL = window.URL.createObjectURL(new Blob([buffer]))
  const fileLink = document.createElement('a')

  fileLink.href = fileURL
  fileLink.setAttribute('download', 'downloadedFile.xlsx') // Set the file name
  document.body.appendChild(fileLink)

  fileLink.click()

  // Clean up and remove the link
  fileLink.parentNode?.removeChild(fileLink)
}
