import Translation from '@/types/translation'
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export interface TranslatableEntity {
	id: number
	translations: Translation[]
}

const cn = (...inputs: ClassValue[]) => {
	return twMerge(clsx(inputs))
}

const getCounterWithUnits = (unitType: string, value: number) => {
	if (unitType.toLowerCase() === 'storage') {
		const bytes = value

		const thresholds = {
			TB: 1e12,
			GB: 1e9,
			MB: 1e6,
			KB: 1e3,
		}

		let result
		let unit

		for (const [unitName, threshold] of Object.entries(thresholds)) {
			if (bytes >= threshold) {
				result = bytes / threshold
				unit = unitName
				break
			}
		}

		if (!result) {
			result = bytes
			unit = 'Bytes'
		} else {
			result = Number(result.toFixed(1))
		}

		return `${result} ${unit}`
	}

	if (unitType.toLowerCase() === 'rounding' && value > 1e6) {
		const result = (value / 1e6).toFixed(1)
		const unit = 'M'

		return `${result} ${unit}`
	} else if (value < 1e6 && unitType.toLowerCase() === 'rounding') {
		const result = value
		return result
	}

	if (unitType.toLowerCase() === 'no rounding') {
		return value
	}

	return value
}

function getTranslation<T extends TranslatableEntity>(
	language: string | null,
	entityType: string,
	entity: T,
	field: string
): Translation | undefined {
	if (!language) return

	return entity?.translations?.find(
		(tr) =>
			tr.language.code === language &&
			tr.field === field &&
			tr.entityType === entityType &&
			tr.entityId === entity.id
	)
}

export { getCounterWithUnits, cn, getTranslation }
