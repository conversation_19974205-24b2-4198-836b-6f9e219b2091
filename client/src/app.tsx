import { AuthProvider } from './context/authContext'
import { Route, Routes } from 'react-router-dom'
import SignIn from './pages/signin'
import routes from './routes/routes'
import PrivateRoute from './components/private-route'
import { Suspense, lazy } from 'react'
import Loader from './components/ui/loader'
import { AppProvider } from './context/appContext'
import {
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query'
import { GoogleOAuthProvider } from '@react-oauth/google'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { GOOGLE_CLIENT_ID } from './api/constants'

const queryClient = new QueryClient()
const DefaultLayout = lazy(() => import('./layout/default-layout'))

export default function App() {
  return (
    <DndProvider backend={HTML5Backend}>
      <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
        <QueryClientProvider client={queryClient}>
          <AppProvider>
            <AuthProvider>
              <Routes>
                <Route path="/auth/signin" element={<SignIn />} />
                <Route element={<DefaultLayout />}>
                  {routes.map((route, index) => {
                    const { path, component: Component } = route
                    return (
                      <Route
                        key={index}
                        path={path}
                        element={
                          <PrivateRoute
                            element={
                              <Suspense fallback={<Loader />}>
                                <Component />
                              </Suspense>
                            }
                          />
                        }
                      />
                    )
                  })}
                </Route>
              </Routes>
            </AuthProvider>
          </AppProvider>
        </QueryClientProvider>
      </GoogleOAuthProvider>
    </DndProvider>
  )
}
