import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { fetchMetrics, fetchMetricsReport } from '@/api/metrics'
import { DEFAULT_CACHE_TIME } from '@/lib/constants'
import Metric from '@/types/metric'

interface UseMetricsProps {
  clientIds: number[]
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
  shouldFetchMetrics: boolean
}

interface UseMetricsReportProps {
  clientIds: number[]
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
  shouldFetchReport: boolean
}

const useMetrics = ({
  clientIds,
  dateRange,
  shouldFetchMetrics,
}: UseMetricsProps) => {
  return useQuery<Metric>({
    queryKey: [
      'metrics',
      {
        clientIds,
        dateFrom: dateRange?.from,
        dateTo: dateRange?.to,
      },
    ],
    queryFn: () =>
      fetchMetrics({
        clientIds,
        dateFrom: dateRange?.from
          ? format(dateRange.from, 'yyyy-MM-dd')
          : '',
        dateTo: dateRange?.to
          ? format(dateRange.to, 'yyyy-MM-dd')
          : '',
      }),
    staleTime: DEFAULT_CACHE_TIME,
    enabled: shouldFetchMetrics,
  })
}

const useMetricsReport = ({
  clientIds,
  dateRange,
  shouldFetchReport,
}: UseMetricsReportProps) => {
  return useQuery({
    queryKey: [
      'metrics-report',
      {
        clientIds,
        dateFrom: dateRange?.from,
        dateTo: dateRange?.to,
      },
    ],
    queryFn: () =>
      fetchMetricsReport({
        clientIds,
        dateFrom: dateRange?.from
          ? format(dateRange.from, 'yyyy-MM-dd')
          : '',
        dateTo: dateRange?.to
          ? format(dateRange.to, 'yyyy-MM-dd')
          : '',
      }),
    staleTime: 0,
    enabled: shouldFetchReport,
  })
}

export { useMetrics, useMetricsReport }
