import React from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom'
import App from './app'
import './index.css'
import './i18n.js'
import { Toaster } from './components/ui/sonner.js'

const container = document.getElementById('root') as HTMLElement
const root = createRoot(container)

root.render(
  <React.StrictMode>
    <Router>
      <App />
      <Toaster />
    </Router>
  </React.StrictMode>
)
