# First stage: compile things.
FROM node:22.12.0-alpine3.19 AS build
WORKDIR /usr/src/app

# (Install OS dependencies; include -dev packages if needed.)

# Install the Javascript dependencies, including all devDependencies.
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm
RUN pnpm install

# Copy the rest of the application in and build it.
COPY . .
# RUN npm build
RUN pnpm run build

# Now /usr/src/app/dist has the built files.

# Second stage: run things.
FROM node:22.12.0-alpine3.19
WORKDIR /usr/src/app

# (Install OS dependencies; just libraries.)

# Install the Javascript dependencies, only runtime libraries.
COPY package.json .
RUN npm install -g pnpm
RUN pnpm install --production

# Copy the dist tree from the first stage.
COPY --from=build /usr/src/app/dist dist

# Run the built application when the container starts.
EXPOSE 3006
CMD ["npm", "run", "serve"]