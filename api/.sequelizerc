const path = require('path')

const isProduction = process.env.NODE_ENV === 'production'

module.exports = {
  config: path.resolve('dist/database', 'config.js'),
  'models-path': path.resolve(
    isProduction ? 'dist/database' : 'src/database',
    'models'
  ),
  'seeders-path': path.resolve('dist/database', 'seeders'),
  'migrations-path': path.resolve(
    isProduction ? 'dist/database' : 'src/database',
    'migrations'
  ),
}
