import { Router, Request, Response } from 'express'
import Counter from '../database/models/counter'
import CounterGroup from '../database/models/counter-group'
import CounterType from '../database/models/counter-type'
import CounterUnit from '../database/models/counter-unit'
import Translation from '../database/models/translation'
import sequelize from '../database/init'
import Language from '../database/models/language'
import { allowRequestToAdmin } from '../middleware/permissionsMiddleware'

const router = Router()

router.get('/', async (req: Request, res: Response) => {
  try {
    const counters = await Counter.scope('withAssociations').findAll({
      order: [['order', 'ASC']],
    })
    res.json(counters)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

router.get('/groups', async (req: Request, res: Response) => {
  try {
    const groups = await CounterGroup.findAll({
      include: [
        {
          model: Counter,
          as: 'counters',
          attributes: [
            'id',
            'name',
            'code',
            'description',
            'order',
            'active',
          ],

          include: [
            {
              model: Translation,
              as: 'translations',
              include: [
                {
                  model: Language,
                  as: 'language',
                  attributes: ['name', 'code'],
                },
              ],
            },
            {
              model: CounterType,
              as: 'type',
              attributes: ['id', 'name'],
            },
            {
              model: CounterUnit,
              as: 'unit',
              attributes: ['id', 'name'],
            },
          ],
        },
      ],
      order: [
        ['order', 'ASC'],
        [{ model: Counter, as: 'counters' }, 'order', 'ASC'],
      ],
    })
    res.json(groups)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

router.get('/types', async (req: Request, res: Response) => {
  try {
    const types = await CounterType.findAll()
    res.json(types)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

router.get('/units', async (req: Request, res: Response) => {
  try {
    const types = await CounterUnit.findAll()
    res.json(types)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

router.get('/:id', async (req: Request, res: Response) => {
  try {
    const counter = await Counter.scope('withAssociations').findByPk(
      req.params.id
    )

    const translations = await Translation.scope(
      'withAssociations'
    ).findAll({
      where: {
        entity_id: counter.id,
        entity_type: 'counter',
      },
    })

    res.json({ ...counter.toJSON(), translations })
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

router.put('/:id', allowRequestToAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()
  try {
    const { id } = req.params

    const updated = await Counter.update(req.body, {
      where: { id },
      transaction,
    })

    const languages = await Language.findAll()

    await Translation.destroy({
      where: {
        entity_id: id,
        entity_type: 'counter',
      },
      transaction,
    })

    await Promise.all(
      req.body.translations.map(async (translation: any) => {
        if (translation.value) {
          const languageId = languages.find(
            (language) => language.code === translation.language
          ).id

          await Translation.create(
            {
              entityId: id,
              entityType: 'counter',
              languageId,
              field: translation.field,
              translation: translation.value,
            },
            { transaction }
          )
        }
      })
    )

    if (updated[0] === 0) {
      return res.status(404).json({ message: 'Counter not found' })
    }

    await transaction.commit()

    const updatedCounter = await Counter.scope(
      'withAssociations'
    ).findByPk(id)
    res.json(updatedCounter)
  } catch (error) {
    if (transaction) await transaction.rollback()
    res.status(500).json({ error: error.message || error.toString() })
  }
})

router.put(
  '/groups/reorder',
  allowRequestToAdmin,
  async (req, res) => {
    const transaction = await sequelize.transaction()
    try {
      const { groups } = req.body
      const offset = 10000

      for (const [groupIndex, group] of groups.entries()) {
        console.log(
          `Updating group ${group.name} with order ${
            offset + groupIndex + 1
          }`
        )
        await CounterGroup.update(
          { order: offset + groupIndex + 1 },
          {
            where: { name: group.name },
            transaction,
          }
        )

        for (const [index, counter] of group.counters.entries()) {
          const uniqueOffsetOrder =
            offset + groupIndex * 1000 + index + 1
          console.log(
            `Updating counter ${counter} with order ${uniqueOffsetOrder}`
          )
          await Counter.update(
            {
              order: uniqueOffsetOrder,
            },
            {
              where: {
                name: counter,
              },
              transaction,
            }
          )
        }
      }

      for (const [groupIndex, group] of groups.entries()) {
        console.log(
          `Updating group ${group.name} with order ${groupIndex + 1}`
        )
        await CounterGroup.update(
          { order: groupIndex + 1 },
          {
            where: { name: group.name },
            transaction,
          }
        )
        for (const [index, counter] of group.counters.entries()) {
          console.log(
            `Updating counter ${counter} with order ${index + 1}`
          )
          const updatedGroup = await CounterGroup.findOne({
            where: { name: group.name },
          })
          await Counter.update(
            {
              order: index + 1,
              groupId: updatedGroup.id,
            },
            {
              where: { name: counter },
              transaction,
            }
          )
        }
      }

      await transaction.commit()
      res.json({ message: 'Groups reordered' })
    } catch (error) {
      console.error(error)
      if (transaction) await transaction.rollback()
      res
        .status(500)
        .json({ error: error.message || error.toString() })
    }
  }
)

export default router
