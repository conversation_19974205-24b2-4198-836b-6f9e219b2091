import { Sequelize } from 'sequelize'
import dotenv from 'dotenv'

dotenv.config()

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT) || 3306,
    dialect: 'mysql',
    retry: {
      max: 5,
      backoffBase: 2000,
      backoffExponent: 1.1,
    },
    logging: false,
    define: {
      underscored: true,
    },
  }
)

export default sequelize
