const languagesExamples = [
  { code: 'es', name: 'spanish' },
  { code: 'en', name: 'english' },
  { code: 'pt', name: 'portuguese' },
]

module.exports = {
  up: async (queryInterface) => {
    const languages = languagesExamples.map((language) => ({
      created_at: new Date(),
      updated_at: new Date(),
      ...language,
    }))

    await queryInterface.bulkInsert('languages', languages)
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('languages', null, {})
  },
}
