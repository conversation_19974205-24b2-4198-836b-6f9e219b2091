const counterGroupsExamples = [
  {
    name: '<PERSON><PERSON>',
    description: 'Estadísticas relacionadas a los agentes conectados',
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Estadísticas relacionadas a los bots',
  },
  { name: '<PERSON><PERSON><PERSON>', description: '<PERSON><PERSON><PERSON>' },
  { name: '<PERSON><PERSON><PERSON>', description: '<PERSON><PERSON><PERSON>' },
  { name: 'Usuarios únicos', description: 'Usuarios únicos' },
  { name: 'Storage', description: 'Storage' },
  { name: 'Incompletos', description: 'Incompletos' },
]

module.exports = {
  up: async (queryInterface) => {
    const existingGroups = await queryInterface.sequelize.query(
      'SELECT name FROM counter_groups',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const existingGroupNames = existingGroups.map(group => group.name);

    const counterGroups = counterGroupsExamples
      .filter(({ name }) => !existingGroupNames.includes(name))
      .map(({ name, description }) => ({
        name,
        description,
        created_at: new Date(),
        updated_at: new Date(),
      }));

    if (counterGroups.length > 0) {
      await queryInterface.bulkInsert('counter_groups', counterGroups);
    }
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('counter_groups', null, {});
  },
}
