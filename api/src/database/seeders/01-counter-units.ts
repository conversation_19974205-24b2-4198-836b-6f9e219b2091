const counterUnitsExamples = [
  {
    name: 'Storage',
    description: 'Storage',
  },
  {
    name: 'Rounding',
    description: 'Rounding',
  },
  {
    name: 'No Rounding',
    description: 'No Rounding',
  },
]

module.exports = {
  up: async (queryInterface) => {
    const counterUnits = counterUnitsExamples.map(
      ({ name, description }) => ({
        name,
        description,
        created_at: new Date(),
        updated_at: new Date(),
      })
    )

    await queryInterface.bulkInsert('counter_units', counterUnits)
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('counter_units', null, {})
  },
}
