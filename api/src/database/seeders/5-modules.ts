const modulesExamples = [
  'ySocial',
  'yFlow',
  'ySurvey',
  'ySmart',
  'Whatsapp',
]

module.exports = {
  up: async (queryInterface) => {
    const modules = modulesExamples.map((name) => ({
      name,
      created_at: new Date(),
      updated_at: new Date(),
    }))

    await queryInterface.bulkInsert('modules', modules)
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('modules', null, {})
  },
}
