const counterTypesExamples = ['max', 'snapshot', 'count', 'unique']

module.exports = {
  up: async (queryInterface) => {
    const counterTypes = counterTypesExamples.map((name) => ({
      name,
      created_at: new Date(),
      updated_at: new Date(),
    }))

    await queryInterface.bulkInsert('counter_types', counterTypes)
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('counter_types', null, {})
  },
}
