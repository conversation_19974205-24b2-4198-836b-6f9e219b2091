import CounterType from '../counter-type'
import CounterGroup from '../counter-group'
import Client from '../client'
import Counter from '../counter'
import Module from '../module'
import ClientMetrics from '../client-metrics'
import ClientCounters from '../client-counters'
import ClientModules from '../client-modules'
import CounterUnit from '../counter-unit'
import Translation from '../translation'
import Language from '../language'

CounterType.hasMany(Counter, { foreignKey: 'typeId' })
Counter.belongsTo(CounterType, { foreignKey: 'typeId', as: 'type' })

Translation.belongsTo(Language, {
  foreignKey: 'languageId',
  as: 'language',
})

Client.belongsToMany(Module, {
  through: ClientModules,
  foreignKey: 'clientId',
  otherKey: 'moduleId',
  as: 'modules',
})

Module.belongsToMany(Client, {
  through: ClientModules,
  foreignKey: 'moduleId',
  otherKey: 'clientId',
})

Client.belongsToMany(Counter, {
  through: ClientCounters,
  foreignKey: 'clientId',
  otherKey: 'counterId',
  as: 'counters',
})

Counter.belongsToMany(Client, {
  through: ClientCounters,
  foreignKey: 'counterId',
  otherKey: 'clientId',
})

ClientMetrics.belongsTo(Client, {
  foreignKey: 'clientId',
  as: 'client',
})
Client.hasMany(ClientMetrics, {
  foreignKey: 'clientId',
  as: 'metrics',
})

ClientMetrics.belongsTo(Module, {
  foreignKey: 'moduleId',
  as: 'module',
})

Module.hasMany(ClientMetrics, {
  foreignKey: 'moduleId',
  as: 'metrics',
})

CounterGroup.hasMany(Counter, {
  foreignKey: 'groupId',
  as: 'counters',
})

Counter.belongsTo(CounterGroup, {
  foreignKey: 'groupId',
  as: 'group',
})

Counter.belongsTo(CounterUnit, {
  foreignKey: 'unitId',
  as: 'unit',
})

Counter.hasMany(Translation, {
  foreignKey: 'entityId',
  constraints: false,
  scope: {
    entityType: 'counter',
  },
  as: 'translations',
})

Translation.belongsTo(Counter, {
  foreignKey: 'entityId',
  constraints: false,
  as: 'counter',
})

export {
  Client,
  Counter,
  Module,
  CounterType,
  CounterGroup,
  ClientCounters,
  ClientModules,
}
