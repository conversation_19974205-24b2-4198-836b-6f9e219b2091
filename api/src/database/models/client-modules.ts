import { Model, DataTypes } from "sequelize";
import sequelize from "../init";

class ClientModules extends Model {
  public clientId!: number;
  public moduleId!: number;
}

ClientModules.init(
  {
    clientId: {
      type: DataTypes.INTEGER,
      references: {
        model: "clients",
        key: "id",
      },
      field: "client_id",
    },
    moduleId: {
      type: DataTypes.INTEGER,
      references: {
        model: "modules",
        key: "id",
      },
      field: "module_id",
    },
  },
  {
    sequelize,
    modelName: "ClientModules",
    tableName: "client_modules",
  }
);

export default ClientModules;
