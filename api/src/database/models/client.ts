import { Model, DataTypes, Association } from 'sequelize'
import sequelize from '../init'
import Module from './module'
import Counter from './counter'

class Client extends Model {
  public id!: number
  public name!: string
  public description!: string
  public active!: boolean
  public isTestClient!: boolean

  public readonly modules?: Module[]
  public readonly counters?: Counter[]

  public static associations: {
    modules: Association<Client, Module>
    counters: Association<Client, Counter>
  }
}

Client.init(
  {
    name: DataTypes.STRING,
    description: DataTypes.STRING,
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    isTestClient: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  },
  {
    sequelize,
    modelName: 'Client',

    scopes: {
      withAssociations: {
        include: [
          {
            model: Counter,
            as: 'counters',
            attributes: [
              'id',
              'name',
              'description',
              'active',
              'code',
            ],
            through: { attributes: [] },
          },
          {
            model: Module,
            as: 'modules',
            attributes: ['id', 'name'],
            through: { attributes: [] },
          },
        ],
      },
    },
  }
)

export default Client
