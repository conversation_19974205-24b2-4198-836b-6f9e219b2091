import { Model, DataTypes, Association } from 'sequelize'
import sequelize from '../init'
import Counter from './counter'

class CounterGroup extends Model {
  public id!: number
  public name!: string
  public description!: string
  public order!: number

  public readonly counters?: Counter[]

  public static associations: {
    counters: Association<CounterGroup, Counter>
  }
}

CounterGroup.init(
  {
    description: DataTypes.STRING,
    name: DataTypes.STRING,
    order: DataTypes.INTEGER,
  },
  {
    sequelize,
    modelName: 'CounterGroup',
    tableName: 'counter_groups',
    indexes: [
      {
        unique: true,
        fields: ['order'],
      },
    ],
  }
)

export default CounterGroup
