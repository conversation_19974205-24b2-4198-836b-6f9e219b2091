import { Model, DataTypes } from 'sequelize'
import CounterType from './counter-type'
import CounterGroup from './counter-group'
import sequelize from '../init'
import Translation from './translation'
import Language from './language'
import CounterUnit from './counter-unit'

class Counter extends Model {
  public id!: number
  public name!: string
  public description!: string
  public active!: boolean
  public order!: number
  public code!: string
  public typeId!: number
  public groupId!: number
  public unitId!: number
  public type!: CounterType
  public group!: CounterGroup
  public unit!: CounterUnit
}

Counter.init(
  {
    name: DataTypes.STRING,
    description: DataTypes.STRING,
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    order: DataTypes.INTEGER,
    code: DataTypes.STRING,
    typeId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'counter_types',
        key: 'id',
      },
    },
    groupId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'counter_groups',
        key: 'id',
      },
    },
    unitId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'counter_units',
        key: 'id',
      },
    },
  },
  {
    sequelize,
    modelName: 'Counter',
    indexes: [
      {
        unique: true,
        fields: ['order', 'group_id'],
      },
    ],
    scopes: {
      withAssociations: {
        include: [
          {
            model: CounterType,
            as: 'type',
            attributes: ['name'],
          },
          {
            model: CounterUnit,
            as: 'unit',
            attributes: ['name'],
          },
          {
            model: CounterGroup,
            as: 'group',
            attributes: ['name'],
          },
          {
            model: Translation,
            as: 'translations', // Ensure this alias matches how you've defined it in associations
            include: [
              {
                model: Language,
                as: 'language',
                attributes: ['name', 'code'],
              },
            ],
          },
        ],
      },
    },
  }
)

export default Counter
