import { Fill } from 'exceljs'

const getCellStyle = (): Fill => ({
  type: 'pattern',
  pattern: 'solid',
  fgColor: { argb: '1a66ff' },
})

const getBorderStyle = () => ({
  top: { style: 'thin' },
  left: { style: 'thin' },
  bottom: { style: 'thin' },
  right: { style: 'thin' },
})

const setMetricsCellStyles = (worksheet) => {
  worksheet.getCell('A1').font = { bold: true }
  worksheet.getCell('B1').font = { bold: true }
  worksheet.getCell('C1').font = { bold: true }

  worksheet.getRow(3).eachCell((cell) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4B5563' }
    }
    cell.font = {
      bold: true,
      color: { argb: 'FFFFFF' }
    }
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
    // Alinear el texto al centro
    cell.alignment = {
      vertical: 'middle',
      horizontal: 'center',
      wrapText: true
    }
  })

  worksheet.columns.forEach((column, index) => {
    if (index === 0) {
      // Primera columna (Cliente)
      column.width = 40
    } else {
      // Columnas de métricas y referencias
      const isMetricColumn = index % 2 === 1
      column.width = isMetricColumn ? 30 : 25
    }

    column.eachCell((cell) => {
      cell.alignment = {
        vertical: 'middle',
        horizontal: index === 0 ? 'left' : 'center',
        wrapText: true
      }
    })
  })
}

const setMetricCellStyle = (worksheet, cell) => {
  worksheet.getCell(cell).fill = getCellStyle()
  worksheet.getCell(cell).border = getBorderStyle() as unknown
}

export { getCellStyle, getBorderStyle, setMetricsCellStyles }
