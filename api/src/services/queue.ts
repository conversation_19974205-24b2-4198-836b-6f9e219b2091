import { ServiceBusClient } from '@azure/service-bus'
import Client from '../database/models/client'
import Module from '../database/models/module'
import sequelize from '../database/init'
import { createMetrics } from './metrics'

const connectionString = process.env.QUEUE_CONNECTION_STRING
const queueName = process.env.QUEUE_NAME

const receiveMessages = async () => {
  const tables = await sequelize.getQueryInterface().showAllTables()
  if (!tables.includes('client_metrics')) {
    console.log('Table does not exist.')
    return
  }

  const modules = await Module.findAll()
  if (modules.length === 0) {
    console.log(
      'We still need to run pnpm run seed before getting metrics'
    )
    return
  }

  // Skip queue processing if connection string or queue name is missing
  if (!connectionString || !queueName) {
    console.log('Queue processing disabled: Missing connection string or queue name')
    return
  }
  const client = new ServiceBusClient(connectionString)
  const receiver = client.createReceiver(queueName)

  const processMessage = async (message) => {
    const transaction = await sequelize.transaction()

    try {
      let { client, date, type } = message.body
      console.log('Processing message: ', message.body)

      console.log('Client value: ', client)

      if (Array.isArray(client)) {
        client = client[0]
      }

      if (typeof client !== 'string') {
        throw new Error('Invalid data: client name must be a string')
      }

      const module = await Module.findOne({
        where: { name: type },
        transaction,
      })
      let clientId = null
      const existingClient = await Client.findOne({
        where: { name: client },
        transaction,
      })

      if (existingClient) {
        clientId = existingClient.id
      } else {
        const newClient = await Client.create(
          {
            name: client,
            active: false,
          },
          { transaction }
        )
        clientId = newClient.id
      }

      await createMetrics(
        {
          clientId,
          date,
          moduleId: module.id,
          metrics: message.body,
        },
        transaction
      )

      await transaction.commit()
      await receiver.completeMessage(message)
    } catch (error) {
      console.error(
        'Error processing message, moving to DLQ: ',
        error
      )

      await transaction.rollback()
      await receiver.deadLetterMessage(message)
    }
  }

  receiver.subscribe({
    processMessage,
    processError: async (err) => {
      console.error('Error: ', err)
    },
  })
}

export { receiveMessages }
