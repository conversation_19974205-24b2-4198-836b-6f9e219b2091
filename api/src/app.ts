import express, { Express, Request, Response } from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import countersRouter from './routes/counters'
import clientsRouter from './routes/clients'
import metricsRouter from './routes/metrics'
import authRouter from './routes/auth'
import modulesRouter from './routes/modules'
import translationsRouter from './routes/translations'
import sequelize from './database/init'
import validateTokenMiddleware from './middleware/validateTokenMiddleware'
import './i18n'
import './database/models/config/associations'
import { receiveMessages } from './services/queue'

dotenv.config()

const app: Express = express()

app.use(cors())
app.use(express.json())

app.use('/api/auth', authRouter)
app.use('/api/counters', validateTokenMiddleware, countersRouter)
app.use('/api/clients', validateTokenMiddleware, clientsRouter)
app.use('/api/metrics', validateTokenMiddleware, metricsRouter)
app.use('/api/modules', validateTokenMiddleware, modulesRouter)
app.use(
  '/api/translations',
  validateTokenMiddleware,
  translationsRouter
)

app.get('/healthcheck', async (_req: Request, res: Response) => {
  const healthcheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
  }
  try {
    await sequelize.authenticate()
    res.status(200).send(healthcheck)
  } catch (e) {
    healthcheck.message = (e as Error).message
    res.status(503).send(healthcheck)
  }
})

const port = process.env.NODE_DOCKER_PORT || 3006

console.log('Waiting for the database to start...')

sequelize
  .authenticate()
  .then(async () => {
    console.log('Connection has been established successfully.')

    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync()
    }

    receiveMessages()
  })
  .catch((err) => {
    console.error('Unable to connect to the database:', err)
  })

app.listen(port, () => {
  return console.log(
    `Express server is listening at http://localhost:${port} 🚀`
  )
})
