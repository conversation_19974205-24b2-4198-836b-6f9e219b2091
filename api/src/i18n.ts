// Import i18next modules
import i18next from 'i18next'
import Backend from 'i18next-fs-backend'
import path from 'path'

// Initialize i18next
i18next.use(Backend).init(
  {
    // Set default language
    lng: 'es',
    // Set fallback language
    fallbackLng: 'es',
    // Define supported languages
    supportedLngs: ['en', 'es', 'pt'],
    // Set the directory path for language files
    backend: {
      loadPath: path.join(__dirname, '/languages/{{lng}}.json'),
    },
    // Enable debug mode if needed
    debug: false,
    // Other options can be added here
  },
  (err, t) => {
    if (err) return console.error(err)
    //console.log('i18next is ready...')
  }
)
