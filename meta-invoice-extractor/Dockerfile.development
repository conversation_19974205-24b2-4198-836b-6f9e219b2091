# Dockerfile para desarrollo con soporte para depuración
FROM node:22 AS development

# Instalar herramientas de depuración adicionales
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    procps \
    net-tools \
    lsof \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copiar archivos de dependencias
COPY package*.json ./
# We'll mount the .env file at runtime instead of copying it into the image

# Instalar todas las dependencias (incluyendo devDependencies)
RUN npm install

# Crear directorios necesarios según .env
RUN mkdir -p /app/input /app/output /app/processed /app/error && \
    chmod 777 /app/input /app/output /app/processed /app/error

# Exponer el puerto de la aplicación (default 3000)
EXPOSE 3000

# Exponer el puerto para el depurador de Node.js
EXPOSE 9229

# No copiar código fuente en la imagen, se montará como volumen
# Esto permite cambios en tiempo real sin reconstruir la imagen

# Definir variables de entorno para desarrollo
ENV NODE_ENV=development

# Establecer el directorio de trabajo actual como WORKDIR
WORKDIR /app

# Comando predeterminado que ejecuta la aplicación en modo de depuración
CMD ["node", "--inspect=0.0.0.0:9229", "src/index.js"]