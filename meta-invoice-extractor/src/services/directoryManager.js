import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

import { createStorageAdapter } from './storage/storageFactory.js';
import InvoiceRepository from './repositories/invoiceRepository.js';
import MySQLInvoiceRepository from './repositories/mysqlInvoiceRepository.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const basePath = path.resolve(__dirname, '../..');
const invoicesDir = process.env.INPUT_DIR;
const outputDir = process.env.OUTPUT_DIR;
const processedDir = process.env.PROCESSED_DIR;
const errorDir = process.env.ERROR_DIR;

const storageType = 'local';
const storageConfig = {
  local: {
    basePath: basePath,
    inputDir: process.env.INPUT_DIR,
    outputDir: process.env.OUTPUT_DIR,
    processedDir: process.env.PROCESSED_DIR,
    errorDir: process.env.ERROR_DIR
  }
};

const mysqlConfig = {
  host: process.env.MYSQL_HOST,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE
};

const useAdditionalRepositories = process.env.USE_ADDITIONAL_REPOSITORIES === 'true';

let instance = null;

class DirectoryManager {
  constructor() {
    if (instance) {
      return instance;
    }
    
    this.initializeStorage();
    instance = this;
  }

  initializeStorage() {
    try {
      this.storageAdapter = createStorageAdapter(storageType, storageConfig[storageType]);
      const config = storageConfig[storageType];
      this.invoiceRepository = new InvoiceRepository(this.storageAdapter, config.outputDir);
      
      this.additionalRepositories = [];
      if (useAdditionalRepositories) {
        if (mysqlConfig.host && mysqlConfig.database) {
          this.additionalRepositories.push(new MySQLInvoiceRepository(mysqlConfig));
        }
      }
      
      this.ensureLocalDirectories();
    } catch (error) {
      console.error('Error initializing storage:', error);
      throw error;
    }
  }

  ensureLocalDirectories() {
    const dirs = [invoicesDir, outputDir, processedDir, errorDir];

    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        console.log(`Creating directory at ${dir}`);
        fs.mkdirSync(dir, { recursive: true });
      }
    });

    return { invoicesDir, outputDir, processedDir, errorDir };
  }

  getDirectories() {
    return { invoicesDir, outputDir, processedDir, errorDir };
  }
  
  getStorageAdapter() {
    return this.storageAdapter;
  }
  
  getInvoiceRepository() {
    return this.invoiceRepository;
  }
  
  getAdditionalRepositories() {
    return this.additionalRepositories;
  }
}

export default new DirectoryManager();
export { invoicesDir, outputDir, processedDir, errorDir };