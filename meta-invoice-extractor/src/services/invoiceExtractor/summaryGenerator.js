import mysql from 'mysql2/promise';

/**
 * Generate specialized account summaries based on invoice data
 * @param {Object} invoiceData - The parsed invoice data
 * @returns {Promise<Object>} - Object containing the two specialized summaries
 */
export async function generateAccountSummaries(invoiceData) {
  try {
    // Generate the conversation type summaries
    const conversationSummaries = generateConversationTypeSummaries(invoiceData.accounts);
    
    // Generate the financial summaries with Yoizen fee
    const financialSummaries = await generateFinancialSummaries(invoiceData.accounts);
    
    return {
      conversationSummaries,
      financialSummaries
    };
  } catch (error) {
    console.error('Error generating account summaries:', error);
    throw error;
  }
}

/**
 * Generate summaries that group conversations by type for each account
 * @param {Array} accounts - List of accounts from the invoice data
 * @returns {Array} - List of account summaries with conversation types
 */
function generateConversationTypeSummaries(accounts) {
  return accounts.map(account => {
    const typeTotals = [];
    const typeMap = new Map();
    
    // Group items by type and sum conversations
    account.items.forEach(item => {
      const type = item.type;
      const currentTotal = typeMap.get(type) || 0;
      typeMap.set(type, currentTotal + item.conversations);
    });
    
    // Convert the map to array format
    typeMap.forEach((conversations, type) => {
      typeTotals.push({ type, conversations });
    });
    
    return {
      name: account.name,
      accountNumber: account.accountNumber,
      typeTotals
    };
  });
}

/**
 * Generate financial summaries with Yoizen fee for each account
 * @param {Array} accounts - List of accounts from the invoice data
 * @returns {Promise<Array>} - List of account financial summaries
 */
async function generateFinancialSummaries(accounts) {
  // Connect to whatsapp database to fetch fee information
  const connection = await createWhatsappDbConnection();
  
  try {
    const summaries = [];
    
    for (const account of accounts) {
      const { accountNumber, totalUSD } = account;
      
      // Fetch fee from whatsapp.accounts table
      const fee = await getYoizenFee(connection, accountNumber);
      
      summaries.push({
        name: account.name,
        accountNumber,
        totalInPdf: totalUSD,
        yoizen_fee: fee
      });
    }
    
    return summaries;
  } finally {
    // Close the connection
    if (connection) await connection.end();
  }
}

/**
 * Create a connection to the whatsapp database
 * @returns {Promise<Object>} - MySQL connection
 */
async function createWhatsappDbConnection() {
  // Get database config from environment variables
  const config = {
    host: process.env.WHATSAPP_DB_HOST,
    user: process.env.WHATSAPP_DB_USER,
    password: process.env.WHATSAPP_DB_PASSWORD,
    database: process.env.WHATSAPP_DB_NAME
  };
  
  return await mysql.createConnection(config);
}

/**
 * Get the Yoizen fee for a specific account
 * @param {Object} connection - Database connection
 * @param {string} accountNumber - Account number (waba_id)
 * @returns {Promise<number>} - The fee or 0 if not found
 */
async function getYoizenFee(connection, accountNumber) {
  try {
    const [rows] = await connection.execute(
      'SELECT yoizen_bsp_fi FROM whatsapp.accounts WHERE waba_id = ?', 
      [accountNumber]
    );
    
    if (rows.length > 0 && rows[0].yoizen_bsp_fi !== undefined) {
      return parseFloat(rows[0].yoizen_bsp_fi);
    }
    
    return 0;
  } catch (error) {
    console.error(`Error fetching Yoizen fee for account ${accountNumber}:`, error);
    return 0;
  }
}