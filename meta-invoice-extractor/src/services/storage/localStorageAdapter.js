import fs from 'fs/promises';
import path from 'path';
import StorageInterface from './storageInterface.js';

export default class LocalStorageAdapter extends StorageInterface {
  constructor(config) {
    super();
    this.basePath = config.basePath;
    this.inputDir = config.inputDir;
    this.outputDir = config.outputDir;
    this.processedDir = config.processedDir;
    this.errorDir = config.errorDir;
  }

  _resolvePath(filePath) {
    return path.isAbsolute(filePath) ? filePath : path.join(this.basePath, filePath);
  }
  
  _mapPath(logicalPath) {
    if (path.isAbsolute(logicalPath)) {
      return logicalPath;
    }
    
    if (logicalPath.startsWith('invoices/') || logicalPath.startsWith('input/')) {
      return path.join(this.basePath, this.inputDir, logicalPath.replace(/^(invoices|input)\//, ''));
    } else if (logicalPath.startsWith('output/')) {
      return path.join(this.basePath, this.outputDir, logicalPath.replace(/^output\//, ''));
    } else if (logicalPath.startsWith('processed/')) {
      return path.join(this.basePath, this.processedDir, logicalPath.replace(/^processed\//, ''));
    } else if (logicalPath.startsWith('error/')) {
      return path.join(this.basePath, this.errorDir, logicalPath.replace(/^error\//, ''));
    }
    
    return this._resolvePath(logicalPath);
  }

  async readFile(filePath) {
    const resolvedPath = this._mapPath(filePath);
    return fs.readFile(resolvedPath);
  }

  async writeFile(filePath, content) {
    const resolvedPath = this._mapPath(filePath);
    
    await fs.mkdir(path.dirname(resolvedPath), { recursive: true });
    
    if (typeof content === 'object' && !Buffer.isBuffer(content)) {
      content = JSON.stringify(content, null, 2);
    }
    
    return fs.writeFile(resolvedPath, content);
  }

  async moveFile(sourcePath, destinationPath) {
    const resolvedSource = this._mapPath(sourcePath);
    const resolvedDestination = this._mapPath(destinationPath);
    
    await fs.mkdir(path.dirname(resolvedDestination), { recursive: true });
    
    return fs.rename(resolvedSource, resolvedDestination);
  }
}