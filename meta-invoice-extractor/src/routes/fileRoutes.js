import express from 'express';
import fs from 'fs';
import path from 'path';
import multer from 'multer';
import directoryManager from '../services/directoryManager.js';

const router = express.Router();

// Set up multer for file uploads
const { invoicesDir } = directoryManager.getDirectories();

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, invoicesDir);
  },
  filename: (req, file, cb) => {
    // Keep original filename or generate a unique one
    cb(null, file.originalname);
  }
});

// File filter to only accept PDFs
const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'application/pdf') {
    cb(null, true);
  } else {
    cb(new Error('Only PDF files are allowed'), false);
  }
};

const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Simple file upload endpoint
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        status: 'error',
        message: 'No file uploaded'
      });
    }
    
    const filename = req.file.filename;
    console.log(`File uploaded successfully: ${filename}`);
    
    // Just return upload success - file watcher will handle processing
    res.status(200).json({
      status: 'success',
      message: 'File uploaded and queued for processing',
      filename: filename,
      filePath: req.file.path
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

export default router;