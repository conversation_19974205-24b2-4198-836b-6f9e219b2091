import express from 'express';
import fs from 'fs/promises';

const router = express.Router();

// Ruta básica de health check
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// Health check detallado
router.get('/health/detailed', async (req, res) => {
  try {
    // Verificar conexión con OpenAI
    let openaiStatus = 'unknown';
    try {
      // Intentar una operación simple con OpenAI
      const openai = req.app.get('openai');
      if (openai) {
        await openai.models.list();
        openaiStatus = 'ok';
      } else {
        openaiStatus = 'not_configured';
      }
    } catch (error) {
      openaiStatus = 'error';
    }

    // Verificar acceso a directorio de prompts
    let promptsStatus = 'unknown';
    try {
      const path = req.app.get('promptsDir');
      await fs.access(path);
      promptsStatus = 'ok';
    } catch (error) {
      promptsStatus = 'error';
    }

    res.status(200).json({
      status: 'ok',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      dependencies: {
        openai: openaiStatus,
        prompts: promptsStatus
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message
    });
  }
});

export default router;