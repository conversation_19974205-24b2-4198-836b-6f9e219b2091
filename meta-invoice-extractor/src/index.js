import express from 'express';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import routes
import healthRoutes from './routes/healthRoutes.js';
import fileRoutes from './routes/fileRoutes.js';

// Import services
import { initInvoiceWatcher, invoicesDir } from './services/invoiceWatcher.js';

// Init express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());

// Register routes
app.use('/api', healthRoutes);
app.use('/api', fileRoutes);

// Watch for invoice file changes
initInvoiceWatcher();

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Invoice directory: ${invoicesDir}`);
  console.log(`Environment: ${process.env.NODE_ENV}`);
});