# Dockerfile optimizado para producción
# Etapa de construcción
FROM node:22-alpine AS builder

WORKDIR /app

# Copiar archivos de dependencias
COPY package*.json ./

# Instalar dependencias
RUN npm ci --only=production

# Copiar código fuente
COPY . .

# Etapa de producción
FROM node:22-alpine AS production

# Configuración para producción
ENV NODE_ENV=production

# Crear usuario no root para seguridad
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001 -G nodejs

WORKDIR /app

# Copiar node_modules y código desde la etapa de construcción
COPY --from=builder /app/node_modules /app/node_modules
COPY --from=builder /app/src /app/src
COPY --from=builder /app/package*.json /app/
# We'll mount the .env file at runtime instead of copying it into the image

# Crear directorios necesarios según .env
RUN mkdir -p /app/invoices /app/output /app/processed /app/error && \
    chown -R nodeuser:nodejs /app

# Cambiar al usuario no privilegiado
USER nodeuser

# Exponer el puerto definido en .env (default 3000)
EXPOSE 3000

# Comando para iniciar la aplicación
CMD ["node", "src/index.js"]